import {
  pgTable,
  uuid,
  varchar,
  text,
  jsonb,
  timestamp,
  pgEnum,
  uniqueIndex,
} from 'drizzle-orm/pg-core';

export const integrationProviderEnum = pgEnum('integration_provider', [
  'stripe',
  //   "slack",
  //   "github",
  //   "notion",
  //   "custom",
]);

export const integrationStatusEnum = pgEnum('integration_status', [
  'pending',
  'connected',
  'error',
  'revoked',
]);

export const environmentEnum = pgEnum('environment', [
  'development',
  'production',
]);

export const orgIntegrations = pgTable(
  'org_integrations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    orgId: varchar('org_id', { length: 128 }).notNull(),
    provider: integrationProviderEnum('provider').notNull(),
    environment: environmentEnum('environment').notNull().default('production'),
    // Provider-side account identifier (e.g., Stripe "acct_123")
    externalId: varchar('external_id', { length: 255 }),
    settings: jsonb('settings')
      .$type<Record<string, unknown>>()
      .default({})
      .notNull(),

    // Reference to secret in a secrets manager (e.g., "vercel:kv:my-secret-key")
    secretRef: text('secret_ref'),

    status: integrationStatusEnum('status').notNull().default('pending'),
    connectedAt: timestamp('connected_at'),
    revokedAt: timestamp('revoked_at'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  t => ({
    // Prevent duplicates per org+provider+environment
    uniq: uniqueIndex('org_integrations_org_provider_env_uniq').on(
      t.orgId,
      t.provider,
      t.environment
    ),
  })
);
