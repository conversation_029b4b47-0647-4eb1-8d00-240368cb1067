'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  HomeIcon,
  BellIcon,
  CalendarIcon,
  UsersIcon,
  CubeIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Home', href: '/home', icon: HomeIcon },
  { name: 'Products', href: '/products', icon: CubeIcon },
];

export function Sidebar() {
  const pathname = usePathname();

  return (
    <div className='w-64 bg-white border-r border-gray-200 flex flex-col'>
      {/* Logo */}
      <div className='p-6'>
        <div className='flex items-center'>
          <div className='w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center'>
            <span className='text-white font-bold text-sm'>A</span>
          </div>
          <span className='ml-3 text-xl font-semibold text-gray-900'>
            alden
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className='flex-1 px-4 pb-4'>
        <ul className='space-y-1'>
          {navigation.map(item => {
            const isActive = pathname === item.href;
            return (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                    ${
                      isActive
                        ? 'bg-purple-50 text-purple-700 border border-purple-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <item.icon
                    className={`
                      mr-3 h-5 w-5 flex-shrink-0
                      ${isActive ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500'}
                    `}
                  />
                  {item.name}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}
