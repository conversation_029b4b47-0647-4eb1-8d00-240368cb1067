{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.preferences.importModuleSpecifier": "relative", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}}