CREATE TYPE "public"."environment" AS ENUM('development', 'production');--> statement-breakpoint
CREATE TYPE "public"."integration_provider" AS ENUM('stripe');--> statement-breakpoint
CREATE TYPE "public"."integration_status" AS ENUM('pending', 'connected', 'error', 'revoked');--> statement-breakpoint
CREATE TABLE "org_integrations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"org_id" varchar(128) NOT NULL,
	"provider" "integration_provider" NOT NULL,
	"environment" "environment" DEFAULT 'production' NOT NULL,
	"external_id" varchar(255),
	"settings" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"secret_ref" text,
	"status" "integration_status" DEFAULT 'pending' NOT NULL,
	"connected_at" timestamp,
	"revoked_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX "org_integrations_org_provider_env_uniq" ON "org_integrations" USING btree ("org_id","provider","environment");