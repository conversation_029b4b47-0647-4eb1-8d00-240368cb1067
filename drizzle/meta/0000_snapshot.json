{"id": "d77203e4-ca42-4d4a-b288-6338bf4b42f1", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.org_integrations": {"name": "org_integrations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "org_id": {"name": "org_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "integration_provider", "typeSchema": "public", "primaryKey": false, "notNull": true}, "environment": {"name": "environment", "type": "environment", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'production'"}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "secret_ref": {"name": "secret_ref", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "integration_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "connected_at": {"name": "connected_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"org_integrations_org_provider_env_uniq": {"name": "org_integrations_org_provider_env_uniq", "columns": [{"expression": "org_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "environment", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.environment": {"name": "environment", "schema": "public", "values": ["development", "production"]}, "public.integration_provider": {"name": "integration_provider", "schema": "public", "values": ["stripe"]}, "public.integration_status": {"name": "integration_status", "schema": "public", "values": ["pending", "connected", "error", "revoked"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}