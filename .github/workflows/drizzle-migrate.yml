name: Run Drizzle Migrate

on:
  workflow_dispatch:
  push:
    branches: [main]

    # Trigger on changes to migration files or the database schema, drizzle folder
    paths:
      - "drizzle/**"
      - .github/workflows/drizzle-migrate.yml

# prevent two migrations from racing if multiple pushes land quickly
concurrency:
  group: drizzle-migrate-${{ github.ref }}
  cancel-in-progress: false

jobs:
  migrate:
    runs-on: ubuntu-latest

    env:
      DATABASE_URL: ${{ secrets.DATABASE_URL }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10 # Or latest stable instead of 10.x
          run_install: true # ensures pnpm is available first

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run drizzle:migrate
        run: pnpm run drizzle:migrate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
